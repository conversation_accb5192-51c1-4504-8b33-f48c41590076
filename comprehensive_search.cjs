const fs = require('fs');
const path = require('path');

// 读取api-erp-values.json
const erpValues = JSON.parse(fs.readFileSync('api-erp-values.json', 'utf8'));

// 递归搜索文件
function searchFiles(dir, results = []) {
  const files = fs.readdirSync(dir);
  
  for (const file of files) {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);
    
    if (stat.isDirectory() && file !== 'node_modules' && file !== '.git') {
      searchFiles(filePath, results);
    } else if (stat.isFile() && /\.(ts|tsx|js|jsx)$/.test(file)) {
      results.push(filePath);
    }
  }
  
  return results;
}

// 搜索文件中的ERP路径
function searchErpPaths(filePath) {
  const content = fs.readFileSync(filePath, 'utf8');
  const lines = content.split('\n');
  const matches = [];
  
  lines.forEach((line, index) => {
    // 搜索 /erp/ 模式
    const erpMatches = line.match(/['"`]([^'"`]*\/erp\/[^'"`]*?)['"`]/g);
    if (erpMatches) {
      erpMatches.forEach(match => {
        const cleanMatch = match.replace(/['"`]/g, '');
        matches.push({
          file: filePath.replace(/\\/g, '/'),
          line: index + 1,
          path: cleanMatch,
          context: 'direct path',
          fullLine: line.trim()
        });
      });
    }
    
    // 搜索模板字符串中的 /erp/
    const templateMatches = line.match(/\$\{[^}]*\}\/erp\/[^'"`\s}]*/g);
    if (templateMatches) {
      templateMatches.forEach(match => {
        matches.push({
          file: filePath.replace(/\\/g, '/'),
          line: index + 1,
          path: match,
          context: 'template string',
          fullLine: line.trim()
        });
      });
    }
    
    // 搜索变量拼接
    const concatMatches = line.match(/[a-zA-Z_$][a-zA-Z0-9_$]*\s*\+\s*['"`]\/erp\/[^'"`]*['"`]/g);
    if (concatMatches) {
      concatMatches.forEach(match => {
        matches.push({
          file: filePath.replace(/\\/g, '/'),
          line: index + 1,
          path: match,
          context: 'variable concatenation',
          fullLine: line.trim()
        });
      });
    }
  });
  
  return matches;
}

// 主搜索逻辑
console.log('开始搜索项目中的ERP路径...');
const files = searchFiles('src');
const allMatches = [];

files.forEach(file => {
  const matches = searchErpPaths(file);
  allMatches.push(...matches);
});

console.log(`找到 ${allMatches.length} 个ERP路径匹配`);

// 分析匹配结果
const matchedPaths = [];
const unmatchedPaths = [];

allMatches.forEach(item => {
  let pathToCheck = item.path;
  
  // 处理不同的路径格式
  if (pathToCheck.startsWith('/erp/')) {
    pathToCheck = pathToCheck.replace('/erp/', '/');
  } else if (pathToCheck.includes('/erp/')) {
    // 提取 /erp/ 后面的部分
    const erpIndex = pathToCheck.indexOf('/erp/');
    pathToCheck = pathToCheck.substring(erpIndex + 4); // +4 for '/erp'
    if (!pathToCheck.startsWith('/')) {
      pathToCheck = '/' + pathToCheck;
    }
  }
  
  const isMatched = erpValues.includes(pathToCheck);
  
  if (isMatched) {
    matchedPaths.push({...item, normalizedPath: pathToCheck});
  } else {
    unmatchedPaths.push({...item, normalizedPath: pathToCheck});
  }
});

console.log('\n匹配结果:');
console.log('匹配的路径数量:', matchedPaths.length);
console.log('不匹配的路径数量:', unmatchedPaths.length);

// 保存结果
fs.writeFileSync('all_erp_matches.json', JSON.stringify(allMatches, null, 2));
fs.writeFileSync('final_matched_paths.json', JSON.stringify(matchedPaths, null, 2));
fs.writeFileSync('final_unmatched_paths.json', JSON.stringify(unmatchedPaths, null, 2));

console.log('\n详细匹配结果:');
matchedPaths.forEach(item => {
  console.log(`✓ ${item.file}:${item.line} - ${item.path} -> ${item.normalizedPath}`);
});

console.log('\n不匹配的路径:');
unmatchedPaths.forEach(item => {
  console.log(`✗ ${item.file}:${item.line} - ${item.path} -> ${item.normalizedPath}`);
});
