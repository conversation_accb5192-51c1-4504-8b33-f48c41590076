[{"file": "src/pages/goodsDetail/server.ts", "line": 28, "path": "/erp/hxl.erp.posorder.readbyserialnumber", "context": "API call", "fullLine": "readbyserialnumber: (data: any) => XlbFetch.post(`${BASE_URL}/erp/hxl.erp.posorder.readbyserialnumber`, data),"}, {"file": "src/pages/goodsDetail/server.ts", "line": 30, "path": "/erp/hxl.erp.storeitemsupplierreport.producer.find", "context": "API call", "fullLine": "producerFind: (data: any) => XlbFetch.post(`${BASE_URL}/erp/hxl.erp.storeitemsupplierreport.producer.find`, data),"}, {"file": "src/pages/goodsDetail/server.ts", "line": 32, "path": "/erp/hxl.erp.storeitemsupplierreport.supplier.find", "context": "API call", "fullLine": "supplierFind: (data: any) => XlbFetch.post(`${BASE_URL}/erp/hxl.erp.storeitemsupplierreport.supplier.find`, data),"}, {"file": "src/pages/samplingDetail/server.ts", "line": 28, "path": "/erp/hxl.erp.posorder.readbyserialnumber", "context": "API call", "fullLine": "readbyserialnumber: (data: any) => XlbFetch.post(`${BASE_URL}/erp/hxl.erp.posorder.readbyserialnumber`, data),"}, {"file": "src/pages/samplingDetail/server.ts", "line": 30, "path": "/erp/hxl.erp.storeitemsupplierreport.producer.find", "context": "API call", "fullLine": "producerFind: (data: any) => XlbFetch.post(`${BASE_URL}/erp/hxl.erp.storeitemsupplierreport.producer.find`, data),"}, {"file": "src/pages/samplingDetail/server.ts", "line": 32, "path": "/erp/hxl.erp.storeitemsupplierreport.supplier.find", "context": "API call", "fullLine": "supplierFind: (data: any) => XlbFetch.post(`${BASE_URL}/erp/hxl.erp.storeitemsupplierreport.supplier.find`, data),"}]