[{"file": "src/constants/config/center.tsx", "line": 2265, "path": "/erp/hxl.erp.org.tree", "context": "direct path", "fullLine": "//       url: '/erp/hxl.erp.org.tree',"}, {"file": "src/constants/config/center.tsx", "line": 2281, "path": "/erp/hxl.erp.org.tree", "context": "direct path", "fullLine": "//       url: '/erp/hxl.erp.org.tree',"}, {"file": "src/pages/goodsDetail/server.ts", "line": 28, "path": "${BASE_URL}/erp/hxl.erp.posorder.readbyserialnumber", "context": "direct path", "fullLine": "readbyserialnumber: (data: any) => XlbFetch.post(`${BASE_URL}/erp/hxl.erp.posorder.readbyserialnumber`, data),"}, {"file": "src/pages/goodsDetail/server.ts", "line": 28, "path": "${BASE_URL}/erp/hxl.erp.posorder.readbyserialnumber", "context": "template string", "fullLine": "readbyserialnumber: (data: any) => XlbFetch.post(`${BASE_URL}/erp/hxl.erp.posorder.readbyserialnumber`, data),"}, {"file": "src/pages/goodsDetail/server.ts", "line": 30, "path": "${BASE_URL}/erp/hxl.erp.storeitemsupplierreport.producer.find", "context": "direct path", "fullLine": "producerFind: (data: any) => XlbFetch.post(`${BASE_URL}/erp/hxl.erp.storeitemsupplierreport.producer.find`, data),"}, {"file": "src/pages/goodsDetail/server.ts", "line": 30, "path": "${BASE_URL}/erp/hxl.erp.storeitemsupplierreport.producer.find", "context": "template string", "fullLine": "producerFind: (data: any) => XlbFetch.post(`${BASE_URL}/erp/hxl.erp.storeitemsupplierreport.producer.find`, data),"}, {"file": "src/pages/goodsDetail/server.ts", "line": 32, "path": "${BASE_URL}/erp/hxl.erp.storeitemsupplierreport.supplier.find", "context": "direct path", "fullLine": "supplierFind: (data: any) => XlbFetch.post(`${BASE_URL}/erp/hxl.erp.storeitemsupplierreport.supplier.find`, data),"}, {"file": "src/pages/goodsDetail/server.ts", "line": 32, "path": "${BASE_URL}/erp/hxl.erp.storeitemsupplierreport.supplier.find", "context": "template string", "fullLine": "supplierFind: (data: any) => XlbFetch.post(`${BASE_URL}/erp/hxl.erp.storeitemsupplierreport.supplier.find`, data),"}, {"file": "src/pages/samplingDetail/server.ts", "line": 28, "path": "${BASE_URL}/erp/hxl.erp.posorder.readbyserialnumber", "context": "direct path", "fullLine": "readbyserialnumber: (data: any) => XlbFetch.post(`${BASE_URL}/erp/hxl.erp.posorder.readbyserialnumber`, data),"}, {"file": "src/pages/samplingDetail/server.ts", "line": 28, "path": "${BASE_URL}/erp/hxl.erp.posorder.readbyserialnumber", "context": "template string", "fullLine": "readbyserialnumber: (data: any) => XlbFetch.post(`${BASE_URL}/erp/hxl.erp.posorder.readbyserialnumber`, data),"}, {"file": "src/pages/samplingDetail/server.ts", "line": 30, "path": "${BASE_URL}/erp/hxl.erp.storeitemsupplierreport.producer.find", "context": "direct path", "fullLine": "producerFind: (data: any) => XlbFetch.post(`${BASE_URL}/erp/hxl.erp.storeitemsupplierreport.producer.find`, data),"}, {"file": "src/pages/samplingDetail/server.ts", "line": 30, "path": "${BASE_URL}/erp/hxl.erp.storeitemsupplierreport.producer.find", "context": "template string", "fullLine": "producerFind: (data: any) => XlbFetch.post(`${BASE_URL}/erp/hxl.erp.storeitemsupplierreport.producer.find`, data),"}, {"file": "src/pages/samplingDetail/server.ts", "line": 32, "path": "${BASE_URL}/erp/hxl.erp.storeitemsupplierreport.supplier.find", "context": "direct path", "fullLine": "supplierFind: (data: any) => XlbFetch.post(`${BASE_URL}/erp/hxl.erp.storeitemsupplierreport.supplier.find`, data),"}, {"file": "src/pages/samplingDetail/server.ts", "line": 32, "path": "${BASE_URL}/erp/hxl.erp.storeitemsupplierreport.supplier.find", "context": "template string", "fullLine": "supplierFind: (data: any) => XlbFetch.post(`${BASE_URL}/erp/hxl.erp.storeitemsupplierreport.supplier.find`, data),"}, {"file": "src/pages/storeProperties/component/batchChange/batchChange.tsx", "line": 365, "path": "/erp/hxl.erp.items.batchimport", "context": "direct path", "fullLine": "//     importUrl={uploadFileType == '商品' ? '/erp/hxl.erp.items.batchimport':'/erp/hxl.erp.storename.import'}"}, {"file": "src/pages/storeProperties/component/batchChange/batchChange.tsx", "line": 365, "path": "/erp/hxl.erp.storename.import", "context": "direct path", "fullLine": "//     importUrl={uploadFileType == '商品' ? '/erp/hxl.erp.items.batchimport':'/erp/hxl.erp.storename.import'}"}]