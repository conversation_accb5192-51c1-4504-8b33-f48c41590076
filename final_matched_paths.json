[{"file": "src/constants/config/center.tsx", "line": 2265, "path": "/erp/hxl.erp.org.tree", "context": "direct path", "fullLine": "//       url: '/erp/hxl.erp.org.tree',", "normalizedPath": "/hxl.erp.org.tree"}, {"file": "src/constants/config/center.tsx", "line": 2281, "path": "/erp/hxl.erp.org.tree", "context": "direct path", "fullLine": "//       url: '/erp/hxl.erp.org.tree',", "normalizedPath": "/hxl.erp.org.tree"}, {"file": "src/pages/storeProperties/component/batchChange/batchChange.tsx", "line": 365, "path": "/erp/hxl.erp.items.batchimport", "context": "direct path", "fullLine": "//     importUrl={uploadFileType == '商品' ? '/erp/hxl.erp.items.batchimport':'/erp/hxl.erp.storename.import'}", "normalizedPath": "/hxl.erp.items.batchimport"}, {"file": "src/pages/storeProperties/component/batchChange/batchChange.tsx", "line": 365, "path": "/erp/hxl.erp.storename.import", "context": "direct path", "fullLine": "//     importUrl={uploadFileType == '商品' ? '/erp/hxl.erp.items.batchimport':'/erp/hxl.erp.storename.import'}", "normalizedPath": "/hxl.erp.storename.import"}]