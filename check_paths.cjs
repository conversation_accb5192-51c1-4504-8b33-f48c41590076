const fs = require('fs');

const erpValues = JSON.parse(fs.readFileSync('api-erp-values.json', 'utf8'));
const tempPaths = JSON.parse(fs.readFileSync('temp_erp_paths.json', 'utf8'));

console.log('检查路径匹配情况:');
const matchedPaths = [];
const unmatchedPaths = [];

tempPaths.forEach(item => {
  const pathToCheck = item.path.replace('/erp/', '/');
  const isMatched = erpValues.includes(pathToCheck);
  
  if (isMatched) {
    matchedPaths.push(item);
  } else {
    unmatchedPaths.push(item);
  }
  
  console.log(`${item.file}:${item.line} - ${item.path} - 匹配: ${isMatched}`);
});

console.log('\n匹配的路径数量:', matchedPaths.length);
console.log('不匹配的路径数量:', unmatchedPaths.length);

// 保存匹配结果
fs.writeFileSync('matched_paths.json', JSON.stringify(matchedPaths, null, 2));
fs.writeFileSync('unmatched_paths.json', JSON.stringify(unmatchedPaths, null, 2));
