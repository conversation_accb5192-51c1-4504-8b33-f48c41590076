[{"file": "src/pages/goodsDetail/server.ts", "line": 28, "path": "${BASE_URL}/erp/hxl.erp.posorder.readbyserialnumber", "context": "direct path", "fullLine": "readbyserialnumber: (data: any) => XlbFetch.post(`${BASE_URL}/erp/hxl.erp.posorder.readbyserialnumber`, data),", "normalizedPath": "/hxl.erp.posorder.readbyserialnumber"}, {"file": "src/pages/goodsDetail/server.ts", "line": 28, "path": "${BASE_URL}/erp/hxl.erp.posorder.readbyserialnumber", "context": "template string", "fullLine": "readbyserialnumber: (data: any) => XlbFetch.post(`${BASE_URL}/erp/hxl.erp.posorder.readbyserialnumber`, data),", "normalizedPath": "/hxl.erp.posorder.readbyserialnumber"}, {"file": "src/pages/goodsDetail/server.ts", "line": 30, "path": "${BASE_URL}/erp/hxl.erp.storeitemsupplierreport.producer.find", "context": "direct path", "fullLine": "producerFind: (data: any) => XlbFetch.post(`${BASE_URL}/erp/hxl.erp.storeitemsupplierreport.producer.find`, data),", "normalizedPath": "/hxl.erp.storeitemsupplierreport.producer.find"}, {"file": "src/pages/goodsDetail/server.ts", "line": 30, "path": "${BASE_URL}/erp/hxl.erp.storeitemsupplierreport.producer.find", "context": "template string", "fullLine": "producerFind: (data: any) => XlbFetch.post(`${BASE_URL}/erp/hxl.erp.storeitemsupplierreport.producer.find`, data),", "normalizedPath": "/hxl.erp.storeitemsupplierreport.producer.find"}, {"file": "src/pages/goodsDetail/server.ts", "line": 32, "path": "${BASE_URL}/erp/hxl.erp.storeitemsupplierreport.supplier.find", "context": "direct path", "fullLine": "supplierFind: (data: any) => XlbFetch.post(`${BASE_URL}/erp/hxl.erp.storeitemsupplierreport.supplier.find`, data),", "normalizedPath": "/hxl.erp.storeitemsupplierreport.supplier.find"}, {"file": "src/pages/goodsDetail/server.ts", "line": 32, "path": "${BASE_URL}/erp/hxl.erp.storeitemsupplierreport.supplier.find", "context": "template string", "fullLine": "supplierFind: (data: any) => XlbFetch.post(`${BASE_URL}/erp/hxl.erp.storeitemsupplierreport.supplier.find`, data),", "normalizedPath": "/hxl.erp.storeitemsupplierreport.supplier.find"}, {"file": "src/pages/samplingDetail/server.ts", "line": 28, "path": "${BASE_URL}/erp/hxl.erp.posorder.readbyserialnumber", "context": "direct path", "fullLine": "readbyserialnumber: (data: any) => XlbFetch.post(`${BASE_URL}/erp/hxl.erp.posorder.readbyserialnumber`, data),", "normalizedPath": "/hxl.erp.posorder.readbyserialnumber"}, {"file": "src/pages/samplingDetail/server.ts", "line": 28, "path": "${BASE_URL}/erp/hxl.erp.posorder.readbyserialnumber", "context": "template string", "fullLine": "readbyserialnumber: (data: any) => XlbFetch.post(`${BASE_URL}/erp/hxl.erp.posorder.readbyserialnumber`, data),", "normalizedPath": "/hxl.erp.posorder.readbyserialnumber"}, {"file": "src/pages/samplingDetail/server.ts", "line": 30, "path": "${BASE_URL}/erp/hxl.erp.storeitemsupplierreport.producer.find", "context": "direct path", "fullLine": "producerFind: (data: any) => XlbFetch.post(`${BASE_URL}/erp/hxl.erp.storeitemsupplierreport.producer.find`, data),", "normalizedPath": "/hxl.erp.storeitemsupplierreport.producer.find"}, {"file": "src/pages/samplingDetail/server.ts", "line": 30, "path": "${BASE_URL}/erp/hxl.erp.storeitemsupplierreport.producer.find", "context": "template string", "fullLine": "producerFind: (data: any) => XlbFetch.post(`${BASE_URL}/erp/hxl.erp.storeitemsupplierreport.producer.find`, data),", "normalizedPath": "/hxl.erp.storeitemsupplierreport.producer.find"}, {"file": "src/pages/samplingDetail/server.ts", "line": 32, "path": "${BASE_URL}/erp/hxl.erp.storeitemsupplierreport.supplier.find", "context": "direct path", "fullLine": "supplierFind: (data: any) => XlbFetch.post(`${BASE_URL}/erp/hxl.erp.storeitemsupplierreport.supplier.find`, data),", "normalizedPath": "/hxl.erp.storeitemsupplierreport.supplier.find"}, {"file": "src/pages/samplingDetail/server.ts", "line": 32, "path": "${BASE_URL}/erp/hxl.erp.storeitemsupplierreport.supplier.find", "context": "template string", "fullLine": "supplierFind: (data: any) => XlbFetch.post(`${BASE_URL}/erp/hxl.erp.storeitemsupplierreport.supplier.find`, data),", "normalizedPath": "/hxl.erp.storeitemsupplierreport.supplier.find"}]