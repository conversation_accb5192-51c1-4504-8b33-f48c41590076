[{"file": "src/constants/config/center.tsx", "line": 2265, "path": "/erp/hxl.erp.org.tree", "context": "commented out code", "fullLine": "//       url: '/erp/hxl.erp.org.tree',"}, {"file": "src/constants/config/center.tsx", "line": 2281, "path": "/erp/hxl.erp.org.tree", "context": "commented out code", "fullLine": "//       url: '/erp/hxl.erp.org.tree',"}, {"file": "src/pages/goodsDetail/server.ts", "line": 28, "path": "/erp/hxl.erp.posorder.readbyserialnumber", "context": "API call", "fullLine": "readbyserialnumber: (data: any) => XlbFetch.post(`${BASE_URL}/erp/hxl.erp.posorder.readbyserialnumber`, data),"}, {"file": "src/pages/goodsDetail/server.ts", "line": 30, "path": "/erp/hxl.erp.storeitemsupplierreport.producer.find", "context": "API call", "fullLine": "producerFind: (data: any) => XlbFetch.post(`${BASE_URL}/erp/hxl.erp.storeitemsupplierreport.producer.find`, data),"}, {"file": "src/pages/goodsDetail/server.ts", "line": 32, "path": "/erp/hxl.erp.storeitemsupplierreport.supplier.find", "context": "API call", "fullLine": "supplierFind: (data: any) => XlbFetch.post(`${BASE_URL}/erp/hxl.erp.storeitemsupplierreport.supplier.find`, data),"}, {"file": "src/pages/samplingDetail/server.ts", "line": 28, "path": "/erp/hxl.erp.posorder.readbyserialnumber", "context": "API call", "fullLine": "readbyserialnumber: (data: any) => XlbFetch.post(`${BASE_URL}/erp/hxl.erp.posorder.readbyserialnumber`, data),"}, {"file": "src/pages/samplingDetail/server.ts", "line": 30, "path": "/erp/hxl.erp.storeitemsupplierreport.producer.find", "context": "API call", "fullLine": "producerFind: (data: any) => XlbFetch.post(`${BASE_URL}/erp/hxl.erp.storeitemsupplierreport.producer.find`, data),"}, {"file": "src/pages/samplingDetail/server.ts", "line": 32, "path": "/erp/hxl.erp.storeitemsupplierreport.supplier.find", "context": "API call", "fullLine": "supplierFind: (data: any) => XlbFetch.post(`${BASE_URL}/erp/hxl.erp.storeitemsupplierreport.supplier.find`, data),"}, {"file": "src/pages/storeProperties/component/batchChange/batchChange.tsx", "line": 365, "path": "/erp/hxl.erp.items.batchimport", "context": "commented out code", "fullLine": "//     importUrl={uploadFileType == '商品' ? '/erp/hxl.erp.items.batchimport':'/erp/hxl.erp.storename.import'}"}, {"file": "src/pages/storeProperties/component/batchChange/batchChange.tsx", "line": 365, "path": "/erp/hxl.erp.storename.import", "context": "commented out code", "fullLine": "//     importUrl={uploadFileType == '商品' ? '/erp/hxl.erp.items.batchimport':'/erp/hxl.erp.storename.import'}"}]